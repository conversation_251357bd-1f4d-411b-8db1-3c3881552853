# 🔐 Credenciales de Prueba - GestiónIP

## 📋 Usuarios Demo Disponibles

### 🔴 Super Administrador
- **Email:** `<EMAIL>`
- **Contraseña:** `Super123!`
- **Permisos:** Acceso completo al sistema
  - ✅ Dashboard
  - ✅ Gestión de usuarios
  - ✅ Reportes y análisis
  - ✅ Configuración del sistema

### 🔵 Administrador
- **Email:** `<EMAIL>`
- **Contraseña:** `Admin123!`
- **Permisos:** Gestión operativa
  - ✅ Dashboard
  - ✅ Gestión de usuarios
  - ✅ Reportes y análisis
  - ❌ Configuración del sistema

### 🟢 Usuario
- **Email:** `<EMAIL>`
- **Contraseña:** `Usuario123!`
- **Permisos:** Acceso básico
  - ✅ Dashboard
  - ❌ Gestión de usuarios
  - ❌ Reportes y análisis
  - ❌ Configuración del sistema

## 🚀 Cómo Probar

1. **Inicia la aplicación:**
   ```bash
   npm run dev
   ```

2. **Accede a:** `http://localhost:5174`

3. **Prueba diferentes roles:**
   - Inicia sesión con cualquiera de las credenciales arriba
   - Observa cómo cambian los permisos y opciones del menú
   - Cierra sesión y prueba con otro rol

## 🎯 Funcionalidades Implementadas

### ✅ Sistema de Autenticación
- Login con validación de credenciales
- Registro de nuevos usuarios (rol: usuario por defecto)
- Persistencia de sesión en localStorage
- Logout seguro

### ✅ Sistema de Roles
- **3 roles:** Usuario, Administrador, Super Administrador
- **Permisos granulares** por funcionalidad
- **Rutas protegidas** según rol
- **UI adaptativa** según permisos

### ✅ Dashboard Administrativo
- **Sidebar dinámico** según permisos del usuario
- **Métricas y estadísticas** (datos demo)
- **Gestión de usuarios** (vista demo)
- **Reportes** (placeholder)
- **Configuración** (solo super admin)

### ✅ Seguridad
- Rutas protegidas con ProtectedRoute
- Verificación de roles en tiempo real
- Redirección automática según estado de auth
- Manejo de errores de autenticación

## 🔧 Arquitectura Técnica

### **Frontend Stack:**
- ⚛️ React 19 + TypeScript
- 🎨 Framer Motion (animaciones)
- 🛣️ React Router DOM (routing)
- 🎯 Context API (estado global)
- 💅 CSS personalizado (diseño moderno)

### **Estructura de Archivos:**
```
src/
├── components/
│   ├── auth/
│   │   ├── AuthContainer.tsx
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── ProtectedRoute.tsx
│   └── dashboard/
│       └── AdminDashboard.tsx
├── contexts/
│   └── AuthContext.tsx
├── types/
│   └── auth.ts
└── App.tsx
```

## 🎨 Características de UI/UX

### ✨ Diseño Moderno
- **Glassmorphism** y efectos visuales
- **Gradientes suaves** y sombras elegantes
- **Animaciones fluidas** con Framer Motion
- **Tipografía profesional** (Inter + Poppins)

### 📱 Responsive Design
- **Mobile-first** approach
- **Grid adaptativo** para formularios
- **Sidebar colapsible** en móviles
- **Notificaciones** posicionadas correctamente

### 🎯 Validación Inteligente
- **Validación en tiempo real** mientras escribes
- **Feedback visual** con colores de borde
- **Requisitos de contraseña** en formato compacto
- **Mensajes de error** claros y útiles

## 🔄 Próximas Mejoras

- [ ] Integración con backend real
- [ ] Gestión completa de usuarios (CRUD)
- [ ] Sistema de permisos más granular
- [ ] Reportes con datos reales
- [ ] Configuraciones del sistema
- [ ] Logs de auditoría
- [ ] Notificaciones push
- [ ] Tema oscuro/claro

---

**¡Disfruta probando el sistema! 🚀**
