export type UserRole = 'usuario' | 'administrador' | 'super_administrador';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  createdAt: Date;
  lastLogin?: Date;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role?: UserRole;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
}

// Datos de usuarios demo con roles
export const demoUsers: Array<User & { password: string }> = [
  {
    id: '1',
    name: 'Super Administrador',
    email: '<EMAIL>',
    password: 'Super123!',
    role: 'super_administrador',
    createdAt: new Date('2024-01-01'),
    lastLogin: new Date()
  },
  {
    id: '2',
    name: 'Administrador',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'administrador',
    createdAt: new Date('2024-01-02'),
    lastLogin: new Date()
  },
  {
    id: '3',
    name: 'Usuario Demo',
    email: '<EMAIL>',
    password: 'Usuario123!',
    role: 'usuario',
    createdAt: new Date('2024-01-03'),
    lastLogin: new Date()
  }
];

export const rolePermissions = {
  usuario: {
    canViewDashboard: true,
    canViewProfile: true,
    canEditProfile: true,
    canViewReports: false,
    canManageUsers: false,
    canManageSystem: false
  },
  administrador: {
    canViewDashboard: true,
    canViewProfile: true,
    canEditProfile: true,
    canViewReports: true,
    canManageUsers: true,
    canManageSystem: false
  },
  super_administrador: {
    canViewDashboard: true,
    canViewProfile: true,
    canEditProfile: true,
    canViewReports: true,
    canManageUsers: true,
    canManageSystem: true
  }
};
